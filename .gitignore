# Environment variables
.env
.env.local
.env.production

# Database files
*.db
*.sqlite
*.sqlite3
data/*.db
data/*.sqlite

# Log files
logs/*.log
logs/*.txt
*.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Audio/Video files (if any are downloaded)
*.mp3
*.mp4
*.wav
*.avi
*.mov

# Config files with sensitive data
config.json
secrets.json

# Backup files
*.bak
*.backup

# Node.js (if using any JS tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
.dockerignore

# Heroku
.env.production

# Local development
local_settings.py
dev_config.py

# FFmpeg binaries (if included)
ffmpeg/
ffmpeg.exe
ffprobe.exe

# Bot-specific
bot_data/
user_data/
guild_data/
cache/

# Playwright browsers
.playwright/
